.header {
  font-family: Arial, sans-serif;
}

.header .top-bar {
  background-color: #0c5447;
  color: white;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 20px;
}

.header .logo-title {
  display: flex;
  align-items: center;
}

.header .logo-title .logo {
  font-weight: bold;
  font-style: italic;
  font-size: 18px;
  margin-right: 10px;
}

.header .logo-title .title {
  font-size: 16px;
  letter-spacing: 1px;
}

.header .icons {
  display: flex;
  gap: 15px;
}

.header .icon {
  width: 20px;
  height: 20px;
  background-size: cover;
  cursor: pointer;
}

.header .icon.search {
  /* background-image: url('/icons/search.svg'); */
}

.header .icon.help {
  /* background-image: url('/icons/help.svg'); */
}

.header .icon.settings {
  /* background-image: url('/icons/settings.svg'); */
}

.header .icon.user {
  /* background-image: url('/icons/user.svg'); */
}

.header .icon.logout {
  /* background-image: url('/icons/logout.svg'); */
}

.header .nav-bar {
  background-color: white;
  padding: 8px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header .nav-links {
  display: flex;
  gap: 20px;
}

.header .nav-links a {
  text-decoration: none;
  color: #666;
  font-weight: bold;
  position: relative;
}

.header .nav-links a.active {
  color: #000;
}

.header .nav-links a.active::after {
  content: '';
  position: absolute;
  bottom: -5px;
  left: 0;
  width: 100%;
  height: 2px;
  background-color: orange;
}

.header .switcher {
  display: flex;
  align-items: center;
  gap: 10px;
}

.header .switcher label {
  font-weight: bold;
}

.header .switcher select {
  padding: 5px;
  border-radius: 4px;
}
