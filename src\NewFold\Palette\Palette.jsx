import React from "react";

export const Palette = ({ className }) => {
  return (
    <svg
      className={`palette ${className}`}
      fill="none"
      height="24"
      viewBox="0 0 24 24"
      width="24"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g className="g" clipPath="url(#clip0_0_2016)">
        <path
          className="path"
          d="M20.056 16.7995L20.156 16.8995C20.383 17.1285 20.6643 17.2963 20.9736 17.3874C21.2829 17.4785 21.6102 17.4899 21.925 17.4205C22.2348 17.3555 22.5233 17.2136 22.7639 17.0079C23.0045 16.8023 23.1896 16.5394 23.302 16.2435C23.8541 14.7938 24.1181 13.2503 24.079 11.6995C23.9665 8.65353 22.7136 5.76099 20.5688 3.59525C18.424 1.4295 15.5438 0.148628 12.499 0.00651556C10.889 -0.0494662 9.28422 0.219306 7.7803 0.796808C6.27638 1.37431 4.90415 2.24872 3.7454 3.3679C2.58665 4.48708 1.66511 5.82813 1.03573 7.31108C0.406342 8.79403 0.081998 10.3885 0.0820313 11.9995C0.0857365 15.181 1.35121 18.2311 3.60084 20.4807C5.85048 22.7303 8.90057 23.9958 12.082 23.9995C12.42 23.9995 12.752 23.9775 13.082 23.9495C13.3324 23.9284 13.5657 23.8138 13.7354 23.6285C13.9052 23.4432 13.9989 23.2008 13.998 22.9495L13.966 19.3615C13.9579 18.6525 14.1613 17.9571 14.5503 17.3642C14.9392 16.7714 15.496 16.3078 16.1496 16.0329C16.8032 15.7579 17.5239 15.684 18.2197 15.8205C18.9155 15.9571 19.5548 16.2979 20.056 16.7995ZM17.114 8.04952C17.4008 7.97343 17.7036 7.98408 17.9843 8.08011C18.265 8.17613 18.5109 8.35323 18.6909 8.589C18.8709 8.82477 18.977 9.10864 18.9957 9.4047C19.0145 9.70076 18.945 9.99572 18.7961 10.2523C18.6472 10.5089 18.4255 10.7155 18.1592 10.8461C17.8928 10.9767 17.5937 11.0254 17.2997 10.9861C17.0057 10.9467 16.7299 10.821 16.5073 10.625C16.2847 10.4289 16.1252 10.1712 16.049 9.88452C15.9984 9.6941 15.9859 9.49558 16.012 9.3003C16.0381 9.10502 16.1025 8.9168 16.2014 8.7464C16.3003 8.57599 16.4318 8.42674 16.5884 8.30717C16.745 8.1876 16.9236 8.10005 17.114 8.04952ZM7.88403 16.9495C7.5973 17.0256 7.29445 17.015 7.01377 16.9189C6.73309 16.8229 6.48719 16.6458 6.30715 16.41C6.12712 16.1743 6.02103 15.8904 6.00232 15.5943C5.9836 15.2983 6.05309 15.0033 6.20199 14.7467C6.3509 14.4902 6.57255 14.2835 6.8389 14.1529C7.10525 14.0223 7.40434 13.9736 7.69837 14.013C7.9924 14.0523 8.26815 14.178 8.49077 14.3741C8.71338 14.5701 8.87286 14.8278 8.94903 15.1145C8.99962 15.3049 9.01221 15.5035 8.98606 15.6987C8.95992 15.894 8.89556 16.0822 8.79666 16.2526C8.69776 16.423 8.56626 16.5723 8.40967 16.6919C8.25308 16.8114 8.07446 16.899 7.88403 16.9495ZM7.88403 10.9495C7.5973 11.0256 7.29445 11.015 7.01377 10.9189C6.73309 10.8229 6.48719 10.6458 6.30715 10.41C6.12712 10.1743 6.02103 9.89039 6.00232 9.59433C5.9836 9.29827 6.05309 9.00331 6.20199 8.74674C6.3509 8.49017 6.57255 8.28351 6.8389 8.1529C7.10525 8.02229 7.40434 7.97359 7.69837 8.01295C7.9924 8.05232 8.26815 8.17798 8.49077 8.37406C8.71338 8.57013 8.87286 8.82781 8.94903 9.11452C8.99962 9.30493 9.01221 9.50345 8.98606 9.69873C8.95992 9.89401 8.89556 10.0822 8.79666 10.2526C8.69776 10.423 8.56626 10.5723 8.40967 10.6919C8.25308 10.8114 8.07446 10.899 7.88403 10.9495ZM12.884 7.94952C12.5973 8.0256 12.2945 8.01495 12.0138 7.91893C11.7331 7.8229 11.4872 7.6458 11.3072 7.41003C11.1271 7.17426 11.021 6.89039 11.0023 6.59433C10.9836 6.29827 11.0531 6.00331 11.202 5.74674C11.3509 5.49017 11.5725 5.28351 11.8389 5.1529C12.1052 5.02229 12.4043 4.97359 12.6984 5.01295C12.9924 5.05232 13.2682 5.17798 13.4908 5.37406C13.7134 5.57013 13.8729 5.82781 13.949 6.11452C13.9996 6.30493 14.0122 6.50345 13.9861 6.69873C13.9599 6.89401 13.8956 7.08223 13.7967 7.25263C13.6978 7.42304 13.5663 7.57229 13.4097 7.69186C13.2531 7.81143 13.0745 7.89898 12.884 7.94952Z"
          fill="#006128"
        />
      </g>

      <defs className="defs">
        <clipPath className="clip-path" id="clip0_0_2016">
          <rect className="rect" fill="white" height="24" width="24" />
        </clipPath>
      </defs>
    </svg>
  );
};
