:root {
  --am-base-dc-theme-black-000: rgba(0, 0, 0, 1);
  --am-base-dc-theme-grey-404040: rgba(64, 64, 64, 1);
  --am-base-dc-theme-grey-light1-7c7c7c: rgba(124, 124, 124, 1);
  --am-base-dc-theme-grey-light2-a3a3a3: rgba(163, 163, 163, 1);
  --am-base-dc-theme-grey-light3-ccc: rgba(204, 204, 204, 1);
  --am-base-dc-theme-grey-light4-f5f5f5: rgba(245, 245, 245, 1);
  --am-base-dc-theme-success-dark-034a1d: rgba(3, 74, 29, 1);
  --am-base-dc-theme-success-light-ebf9ed: rgba(235, 249, 237, 1);
  --am-base-dc-theme-warning-dark-be5a00: rgba(190, 90, 0, 1);
  --am-base-dc-theme-warning-f2b61b: rgba(242, 182, 27, 1);
  --am-base-dc-theme-white-fff: rgba(255, 255, 255, 1);
  --am-brand-amm-theme-primary: rgba(0, 122, 51, 1);
  --am-brand-amm-theme-primary-dark1: rgba(0, 97, 40, 1);
  --am-brand-amm-theme-primary-light1: rgba(64, 155, 102, 1);
  --am-brand-amm-theme-primary-light3: rgba(204, 228, 214, 1);
  --am-brand-amm-theme-primary-light4: rgba(229, 242, 235, 1);
  --am-brand-amm-theme-secondary-light1: rgba(64, 144, 188, 1);
  --am-brand-amm-theme-secondary-light4: rgba(229, 240, 246, 1);
  --black: rgba(0, 0, 0, 1);
  --box-shadow-shadow: 0px 0px 0px 0px rgba(0, 0, 0, 0), 0px 2px 4px 0px
    rgba(0, 0, 0, 0.04);
  --box-shadow-shadow-sm: 0px 1px 2px 0px rgba(0, 0, 0, 0.04);
  --h2-subheader-font-family: "Open Sans", Helvetica;
  --h2-subheader-font-size: 20px;
  --h2-subheader-font-style: normal;
  --h2-subheader-font-weight: 400;
  --h2-subheader-letter-spacing: 0.15000000596046448px;
  --h2-subheader-line-height: normal;
  --primarybrand-green: rgba(0, 122, 51, 1);
  --secondarygrey-base-textgrey-1: rgba(132, 138, 149, 1);
  --shadow-2: 0px 2px 4px 0px rgba(0, 0, 0, 0.12), 0px 4px 6px 0px
    rgba(0, 0, 0, 0.06);
  --shadow-3: 0px 10px 16px 0px rgba(0, 0, 0, 0.1), 0px 4px 6px 0px
    rgba(0, 0, 0, 0.06);
  --x-mdc-theme-primary-007a33: rgba(0, 122, 51, 1);
}
