import React from "react";

export const Fill1 = ({ className }) => {
  return (
    <svg
      className={`fill-1 ${className}`}
      fill="none"
      height="12"
      viewBox="0 0 12 12"
      width="12"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g className="g" clipPath="url(#clip0_0_1728)">
        <path
          className="path"
          d="M10.7247 6.93199L6.38872 11.268C5.91949 11.7361 5.28376 11.999 4.62097 11.999C3.95818 11.999 3.32244 11.7361 2.85322 11.268L0.732216 9.14649C0.263386 8.67765 0 8.04177 0 7.37874C0 6.71571 0.263386 6.07983 0.732216 5.61099L3.68222 2.66099L2.25522 1.21449C2.20401 1.16902 2.1627 1.11351 2.13383 1.05141C2.10497 0.989315 2.08916 0.921949 2.0874 0.853492C2.08565 0.785034 2.09797 0.716946 2.12361 0.653446C2.14925 0.589947 2.18765 0.532392 2.23645 0.484348C2.28525 0.436305 2.3434 0.398797 2.40729 0.374151C2.47118 0.349506 2.53945 0.338247 2.60788 0.341073C2.6763 0.343899 2.74341 0.360751 2.80505 0.390582C2.86669 0.420413 2.92155 0.462588 2.96622 0.514493L7.45122 5.05499L7.45572 5.06149C7.55186 5.14751 7.67765 5.19281 7.80655 5.18786C7.93546 5.1829 8.0574 5.12807 8.14665 5.03493C8.2359 4.94178 8.28548 4.81762 8.28493 4.68862C8.28439 4.55962 8.23375 4.43587 8.14372 4.34349L4.53872 0.693493C4.49395 0.586663 4.48766 0.467604 4.52093 0.356653C4.55419 0.245701 4.62494 0.149741 4.7211 0.085163C4.81726 0.0205851 4.93286 -0.00860392 5.04815 0.00258179C5.16344 0.0137675 5.27127 0.064634 5.35322 0.146493L11.8532 6.64649C11.901 6.69262 11.9391 6.74779 11.9653 6.80879C11.9915 6.86979 12.0053 6.9354 12.0058 7.00179C12.0064 7.06818 11.9938 7.13402 11.9686 7.19547C11.9435 7.25692 11.9064 7.31274 11.8594 7.35969C11.8125 7.40664 11.7566 7.44376 11.6952 7.4689C11.6337 7.49404 11.5679 7.50669 11.5015 7.50612C11.4351 7.50554 11.3695 7.49175 11.3085 7.46554C11.2475 7.43934 11.1923 7.40125 11.1462 7.35349L10.7247 6.93199ZM10.3457 9.21549C9.98022 9.74599 9.49972 10.3625 9.49972 10.75C9.49972 11.0815 9.63141 11.3995 9.86583 11.6339C10.1003 11.8683 10.4182 12 10.7497 12C11.0812 12 11.3992 11.8683 11.6336 11.6339C11.868 11.3995 11.9997 11.0815 11.9997 10.75C11.9997 10.3255 11.5272 9.72799 11.1632 9.21099C11.1167 9.14573 11.0552 9.09261 10.9838 9.0561C10.9125 9.01959 10.8334 9.00076 10.7533 9.0012C10.6732 9.00164 10.5943 9.02134 10.5234 9.05863C10.4524 9.09593 10.3915 9.14972 10.3457 9.21549Z"
          fill="#007A33"
        />
      </g>

      <defs className="defs">
        <clipPath className="clip-path" id="clip0_0_1728">
          <rect className="rect" fill="white" height="12" width="12" />
        </clipPath>
      </defs>
    </svg>
  );
};
