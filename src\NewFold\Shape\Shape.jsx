import React from "react";

export const Shape = ({ className }) => {
  return (
    <svg
      className={`shape ${className}`}
      fill="none"
      height="20"
      viewBox="0 0 20 20"
      width="20"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        className="path"
        d="M3.5 0C5.10569 0 6.45791 1.08153 6.87012 2.55566L13.1768 3.39941C13.6381 2.00565 14.9515 1 16.5 1C18.433 1 20 2.567 20 4.5C20 6.08536 18.9455 7.42311 17.5 7.85352V11.1455C18.9457 11.5758 20 12.9145 20 14.5C20 16.433 18.433 18 16.5 18C15.0492 18 13.8051 17.117 13.2744 15.8594L12.5117 15.9619L12.2471 13.9795L13.0566 13.8711C13.293 12.5685 14.2501 11.5176 15.5 11.1455V7.85352C14.3413 7.5085 13.4345 6.58077 13.1201 5.40918L6.83301 4.56836C6.48194 5.66449 5.60571 6.52429 4.5 6.85352V13.1455C5.36069 13.4017 6.0811 13.9805 6.52441 14.7412L7.40918 14.624L7.67383 16.6064L6.99316 16.6963C6.89131 18.5379 5.3671 20 3.5 20C1.567 20 0 18.433 0 16.5C0 14.9145 1.05434 13.5758 2.5 13.1455V6.85352C1.05447 6.42311 0 5.08536 0 3.5C0 1.567 1.567 0 3.5 0ZM3.5 15C2.67157 15 2 15.6716 2 16.5C2 17.3284 2.67157 18 3.5 18C4.32843 18 5 17.3284 5 16.5C5 15.6716 4.32843 15 3.5 15ZM11.3018 16.123L8.88281 16.4453L8.61914 14.4629L11.0381 14.1406L11.3018 16.123ZM16.5 13C15.6716 13 15 13.6716 15 14.5C15 15.3284 15.6716 16 16.5 16C17.3284 16 18 15.3284 18 14.5C18 13.6716 17.3284 13 16.5 13ZM16.5 3C15.6716 3 15 3.67157 15 4.5C15 5.32843 15.6716 6 16.5 6C17.3284 6 18 5.32843 18 4.5C18 3.67157 17.3284 3 16.5 3ZM3.5 2C2.67157 2 2 2.67157 2 3.5C2 4.32843 2.67157 5 3.5 5C4.32843 5 5 4.32843 5 3.5C5 2.67157 4.32843 2 3.5 2Z"
        fill="#666666"
      />
    </svg>
  );
};
