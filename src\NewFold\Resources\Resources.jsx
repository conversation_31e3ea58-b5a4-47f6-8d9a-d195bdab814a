import React from "react";

export const Resources = ({ className }) => {
  return (
    <svg
      className={`resources ${className}`}
      fill="none"
      height="24"
      viewBox="0 0 24 24"
      width="24"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g className="g" clipPath="url(#clip0_0_2002)">
        <path
          className="path"
          d="M23.0001 2.99962H13.0001C12.7349 2.99962 12.4805 3.10497 12.293 3.29251C12.1054 3.48005 12.0001 3.7344 12.0001 3.99962V7.26961L9.60007 3.10962C9.42493 2.80419 9.17228 2.55041 8.86763 2.37392C8.56298 2.19743 8.21714 2.10449 7.86507 2.10449C7.51299 2.10449 7.16716 2.19743 6.86251 2.37392C6.55786 2.55041 6.3052 2.80419 6.13007 3.10962L0.130068 13.4996C0.0423 13.6516 -0.00390625 13.8241 -0.00390625 13.9996C-0.00390625 14.1751 0.0423 14.3476 0.130068 14.4996C0.218173 14.6522 0.345037 14.7788 0.497814 14.8666C0.650592 14.9544 0.823858 15.0003 1.00007 14.9996H5.29007C5.09591 15.6485 4.9982 16.3223 5.00007 16.9996C5.00007 18.8561 5.73757 20.6366 7.05032 21.9493C8.36308 23.2621 10.1436 23.9996 12.0001 23.9996C13.8566 23.9996 15.6371 23.2621 16.9498 21.9493C18.2626 20.6366 19.0001 18.8561 19.0001 16.9996C19.0019 16.3223 18.9042 15.6485 18.7101 14.9996H23.0001C23.2653 14.9996 23.5196 14.8942 23.7072 14.7067C23.8947 14.5192 24.0001 14.2648 24.0001 13.9996V3.99962C24.0001 3.7344 23.8947 3.48005 23.7072 3.29251C23.5196 3.10497 23.2653 2.99962 23.0001 2.99962ZM2.73007 12.9996L7.86007 4.10962L11.2901 9.99961C10.29 10.1109 9.32568 10.4365 8.46283 10.9542C7.59998 11.4719 6.85889 12.1696 6.29007 12.9996H2.73007ZM12.0001 21.9996C11.0112 21.9996 10.0445 21.7063 9.22222 21.1569C8.39997 20.6075 7.75911 19.8266 7.38067 18.913C7.00223 17.9994 6.90322 16.9941 7.09614 16.0241C7.28907 15.0542 7.76527 14.1633 8.46454 13.4641C9.1638 12.7648 10.0547 12.2886 11.0246 12.0957C11.9945 11.9028 12.9999 12.0018 13.9135 12.3802C14.8271 12.7586 15.608 13.3995 16.1574 14.2218C16.7068 15.044 17.0001 16.0107 17.0001 16.9996C17.0001 18.3257 16.4733 19.5974 15.5356 20.5351C14.5979 21.4728 13.3262 21.9996 12.0001 21.9996ZM22.0001 12.9996H17.7401C16.8319 11.7031 15.515 10.7488 14.0001 10.2896V4.99961H22.0001V12.9996Z"
          fill="#7C7C7C"
        />
      </g>

      <defs className="defs">
        <clipPath className="clip-path" id="clip0_0_2002">
          <rect className="rect" fill="white" height="24" width="24" />
        </clipPath>
      </defs>
    </svg>
  );
};
