import React from "react";

export const StencilTop = ({ className }) => {
  return (
    <svg
      className={`stencil-top ${className}`}
      fill="none"
      height="610"
      viewBox="0 0 64 610"
      width="64"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        className="path"
        clipRule="evenodd"
        d="M0 0H64V610H0V0ZM53 45C53 50.3785 50.9783 55.2847 47.6528 59L47.6714 59.0208C44.7092 62.8984 42.95 67.7437 42.95 72.9999C42.95 78.2562 44.7092 83.1016 47.6714 86.9792L47.6528 87C50.978 90.7153 53 95.6216 53 101C53 106.378 50.978 111.285 47.6528 115L47.6714 115.021C44.7092 118.898 42.95 123.744 42.95 129C42.95 134.256 44.7092 139.102 47.6714 142.979L47.6528 143C50.978 146.715 53 151.622 53 157C53 162.378 50.978 167.285 47.6528 171L47.6714 171.021C44.7092 174.898 42.95 179.744 42.95 185C42.95 190.256 44.7092 195.102 47.6714 198.979L47.6528 199C50.978 202.715 53 207.622 53 213C53 218.378 50.978 223.285 47.6528 227L47.6714 227.021C44.7092 230.898 42.95 235.744 42.95 241C42.95 246.256 44.7092 251.102 47.6714 254.979L47.6528 255C50.978 258.715 53 263.622 53 269C53 280.598 43.5979 290 32 290C20.4021 290 11 280.598 11 269C11 263.568 13.0623 258.618 16.4465 254.89C19.3674 251.027 21.1001 246.216 21.1001 241C21.1001 235.784 19.3677 230.973 16.447 227.111C13.0625 223.382 11 218.432 11 213C11 207.568 13.0623 202.618 16.4465 198.89C19.3674 195.027 21.1001 190.216 21.1001 185C21.1001 179.784 19.3677 174.973 16.447 171.111C13.0625 167.382 11 162.432 11 157C11 151.568 13.0623 146.618 16.4465 142.89C19.3674 139.027 21.1001 134.216 21.1001 129C21.1001 123.784 19.3677 118.973 16.447 115.111C13.0625 111.382 11 106.432 11 101C11 95.5682 13.0623 90.618 16.4465 86.8898C19.3674 83.0272 21.1001 78.2159 21.1001 72.9999C21.1001 67.7842 19.3677 62.9731 16.447 59.1107C16.644 59.3278 16.8457 59.5408 17.0518 59.7495C13.3098 55.9574 11 50.7484 11 45C11 33.4021 20.4021 24 32 24C43.5979 24 53 33.4021 53 45ZM53 341C53 346.379 50.9783 351.285 47.6528 355L47.6714 355.021C44.7092 358.898 42.95 363.744 42.95 369C42.95 374.256 44.7092 379.102 47.6714 382.979L47.6528 383C50.978 386.715 53 391.622 53 397C53 402.378 50.978 407.285 47.6528 411L47.6714 411.021C44.7092 414.898 42.95 419.744 42.95 425C42.95 430.256 44.7092 435.102 47.6714 438.979L47.6528 439C50.978 442.715 53 447.622 53 453C53 458.378 50.978 463.285 47.6528 467L47.6714 467.021C44.7095 470.899 42.95 475.744 42.95 481C42.95 486.256 44.7095 491.102 47.6714 494.979L47.6528 495C50.978 498.715 53 503.622 53 509C53 514.378 50.978 519.285 47.6528 523L47.6714 523.021C44.7095 526.899 42.95 531.744 42.95 537C42.95 542.256 44.7095 547.102 47.6714 550.979L47.6528 551C50.978 554.715 53 559.622 53 565C53 576.598 43.5979 586 32 586C20.4021 586 11 576.598 11 565C11 559.568 13.0627 554.617 16.4478 550.889C19.3679 547.026 21.1001 542.215 21.1001 537C21.1001 531.784 19.3672 526.972 16.446 523.109C13.062 519.381 11 514.432 11 509C11 503.567 13.0623 498.617 16.4478 494.889C19.3679 491.026 21.1001 486.215 21.1001 481C21.1001 475.784 19.3672 470.972 16.446 467.109C13.062 463.381 11 458.432 11 453C11 447.568 13.0623 442.618 16.4465 438.89C19.3674 435.027 21.1001 430.216 21.1001 425C21.1001 419.784 19.3677 414.973 16.447 411.111C13.0625 407.382 11 402.432 11 397C11 391.568 13.0623 386.618 16.4465 382.89C19.3674 379.027 21.1001 374.216 21.1001 369C21.1001 363.784 19.3677 358.973 16.447 355.111C13.0625 351.382 11 346.432 11 341C11 329.402 20.4021 320 32 320C43.5979 320 53 329.402 53 341ZM32 64C42.4934 64 51 55.4934 51 45C51 34.5066 42.4934 26 32 26C21.5066 26 13 34.5066 13 45C13 55.4934 21.5066 64 32 64ZM32 120C42.4934 120 51 111.493 51 101C51 90.5066 42.4934 82 32 82C21.5066 82 13 90.5066 13 101C13 111.493 21.5066 120 32 120ZM51 157C51 167.493 42.4934 176 32 176C21.5066 176 13 167.493 13 157C13 146.507 21.5066 138 32 138C42.4934 138 51 146.507 51 157ZM32 232C42.4934 232 51 223.493 51 213C51 202.507 42.4934 194 32 194C21.5066 194 13 202.507 13 213C13 223.493 21.5066 232 32 232ZM51 269C51 279.493 42.4934 288 32 288C21.5066 288 13 279.493 13 269C13 258.507 21.5066 250 32 250C42.4934 250 51 258.507 51 269ZM32 360C42.4934 360 51 351.493 51 341C51 330.507 42.4934 322 32 322C21.5066 322 13 330.507 13 341C13 351.493 21.5066 360 32 360ZM51 397C51 407.493 42.4934 416 32 416C21.5066 416 13 407.493 13 397C13 386.507 21.5066 378 32 378C42.4934 378 51 386.507 51 397ZM32 472C42.4934 472 51 463.493 51 453C51 442.507 42.4934 434 32 434C21.5066 434 13 442.507 13 453C13 463.493 21.5066 472 32 472ZM51 509C51 519.493 42.4934 528 32 528C21.5066 528 13 519.493 13 509C13 498.507 21.5066 490 32 490C42.4934 490 51 498.507 51 509ZM32 584C42.4934 584 51 575.493 51 565C51 554.507 42.4934 546 32 546C21.5066 546 13 554.507 13 565C13 575.493 21.5066 584 32 584Z"
        fill="white"
        fillRule="evenodd"
      />
    </svg>
  );
};
